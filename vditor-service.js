class VditorService {
  constructor() {
    this.content = '';
    this.initialized = false;
    this.marked = null;
  }

  /**
   * 初始化服务
   * @param {Object} options - 配置选项
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    try {
      // 动态导入 marked
      const markedModule = await import('marked');
      this.marked = markedModule.marked;

      // 配置 marked
      this.marked.setOptions({
        breaks: true,
        gfm: true,
        ...options
      });

      this.initialized = true;
      console.log('Vditor 服务初始化成功');
    } catch (error) {
      console.error('Vditor 服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取 HTML 内容 (类似 vditor.value.getHTML())
   * @returns {string} HTML 内容
   */
  getHTML() {
    if (!this.initialized || !this.marked) {
      throw new Error('Vditor 服务未初始化，请先调用 initialize() 方法');
    }

    try {
      // 将 Markdown 转换为 HTML
      return this.marked(this.content);
    } catch (error) {
      console.error('获取 HTML 内容失败:', error);
      return '';
    }
  }

  /**
   * 设置内容
   * @param {string} content - 要设置的 Markdown 内容
   * @param {boolean} clearStack - 是否清空撤销栈（兼容参数，实际不使用）
   */
  setValue(content, clearStack = false) {
    if (!this.initialized) {
      throw new Error('Vditor 服务未初始化，请先调用 initialize() 方法');
    }

    try {
      this.content = content || '';
    } catch (error) {
      console.error('设置内容失败:', error);
    }
  }

  /**
   * 获取 Markdown 内容
   * @returns {string} Markdown 内容
   */
  getValue() {
    if (!this.initialized) {
      throw new Error('Vditor 服务未初始化，请先调用 initialize() 方法');
    }

    try {
      return this.content;
    } catch (error) {
      console.error('获取 Markdown 内容失败:', error);
      return '';
    }
  }

  /**
   * 销毁服务实例
   */
  destroy() {
    this.content = '';
    this.initialized = false;
    console.log('Vditor 服务已销毁');
  }
}

module.exports = VditorService;
